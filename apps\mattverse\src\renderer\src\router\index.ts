import { createRouter, createWebHashHistory, type RouteRecordRaw } from 'vue-router'
// import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/auth',
    name: 'auth',
    component: () => import('@/pages/base/auth/index.vue'),
  },
  {
    path: '/',
    component: () => import('@/pages/layout/index.vue'),
    redirect: '/workflow',
    children: [
    //工作流
      {
        path: '/workflow',
        name: 'workflow',
        component: () => import('@/pages/base/workflow/index.vue'),
        meta: {
          title: '工作流',
          icon: 'Workflow',
          sort: 1,
          isActive: false,
          showInMenu: true,
        },
        children: [
          {
            name: 'workflow-editor',
            path: 'editor/:id',
            component: () => import('@/pages/base/workflow/components/edit/index.vue'),
            meta: {
              title: '工作流编辑',
              icon: 'Workflow',
              sort: 1,
              isActive: false,
              showInMenu: false,
            },
          },
        ],
      },
      //计算任务
      {
        path: '/task',
        name: 'task',
        component: () => import('@/pages/base/task/index.vue'),
        meta: {
          title: '计算任务',
          icon: 'Clock4',
          sort: 2,
          isActive: false,
          showInMenu: true,
        },
      },
      //服务器状态
      {
        path: '/server',
        name: 'server',
        component: () => import('@/pages/base/server/index.vue'),
        meta: {
          title: '服务器状态',
          icon: 'Database',
          sort: 3,
          isActive: false,
          showInMenu: true,
        },
      },
      //日志
      {
        path: '/logger',
        name: 'logger',
        component: () => import('@/pages/base/logger/index.vue'),
        meta: {
          title: '日志',
          icon: 'FileClock',
          sort: 4,
          isActive: false,
          showInMenu: true,
        },
      },
      //工具管理
      {
        path: '/tools',
        name: 'tools',
        component:  () => import('@/pages/base/tools/index.vue'),
        redirect: '/tools/node', // 默认重定向到第一个子路由
        meta: {
          title: '工具管理',
          icon: 'Blocks',
          sort: 5,
          isActive: false,
          showInMenu: true,
        },
        children: [
          {
            path: '/tools/node',
            name: 'node',
            component:  () => import('@/pages/base/tools/node/index.vue'),
            meta: {
              title: '节点工具模块',
              icon: 'Workflow',
              sort: 1,
              isActive: false,
              showInMenu: true,
            },
          },
          {
            path: '/tools/agent',
            name: 'agent',
            component:  () => import('@/pages/base/tools/agent/index.vue'),
            meta: {
              title: 'Agent工具模块',
              icon: 'Bot',
              sort: 2,
              isActive: false,
              showInMenu: true,
            },
          },
          {
            path: '/tools/other',
            name: 'other',
            component:  () => import('@/pages/base/tools/other/index.vue'),
            meta: {
              title: '其它工具模块',
              icon: 'Bolt',
              sort: 3,
              isActive: false,
              showInMenu: true,
            },
          },
        ]
      },
      //设置
      {
        path: '/setting',
        name: 'setting',
        component:  () => import('@/pages/base/setting/index.vue'),
        redirect: '/setting/basic', // 默认重定向到第一个子路由
        meta: {
          title: '设置',
          icon: 'Settings',
          sort: 6,
          isActive: false,
          showInMenu: true,
        },
        children: [
          {
            path: '/setting/basic',
            name: 'basic',
            component: () => import('@/pages/base/setting/basic/index.vue'),
            meta: {
              title: '基本设置',
              icon: 'Bolt',
              sort: 1,
              isActive: false,
              showInMenu: true,
            },
          },
          {
            path: '/setting/middleware',
            name: 'middleware',
            component: () => import('@/pages/base/setting/middleware/index.vue'),
            meta: {
              title: '中台设置',
              icon: 'SquareTerminal',
              sort: 2,
              isActive: false,
              showInMenu: true,
            },
          },
          {
            path: '/setting/flow',
            name: 'flow',
            component: () => import('@/pages/base/setting/flow/index.vue'),
            meta: {
              title: '流程设置',
              icon: 'Workflow',
              sort: 3,
              isActive: false,
              showInMenu: true,
            },
          },
          {
            path: '/setting/about',
            name: 'about',
            component: () => import('@/pages/base/setting/about/index.vue'),
            meta: {
              title: '关于我们',
              icon: 'Info',
              sort: 4,
              isActive: false,
              showInMenu: true,
            },
          },
        ]
      },
    ],
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router
