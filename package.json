{"name": "mattverse-monorepo", "version": "1.0.2", "private": true, "type": "module", "license": "MIT", "description": "Mattverse Monorepo - Electron applications with shared packages", "author": "mattverse.com", "workspaces": ["apps/*", "packages/*"], "scripts": {"//": "=== 基础工具 ===", "prepare": "husky", "format": "prettier --write .", "format:check": "prettier --check .", "commit": "git-cz", "setup": "pnpm install && pnpm build:packages && pnpm health-check", "health-check": "node scripts/maintenance/health-check.js", "project-info": "node scripts/utils/project-info.js", "scripts": "node scripts/utils/interactive-launcher.js", "//build": "=== 构建命令 ===", "build": "turbo run build", "build:apps": "turbo run build --filter=./apps/*", "build:packages": "turbo run build --filter=./packages/*", "//build-packages": "=== 包构建 ===", "build:shared": "turbo run build --filter=@mattverse/shared", "build:ui": "turbo run build --filter=@mattverse/mattverse-ui", "build:flow": "turbo run build --filter=@mattverse/mattverse-flow", "build:core": "turbo run build --filter=@mattverse/electron-core", "build:configs": "turbo run build --filter=@mattverse/configs", "build:i18n": "turbo run build --filter=@mattverse/i18n", "//build-apps": "=== 应用构建 ===", "build:mattverse": "turbo run build --filter=mattverse", "build:highpower": "turbo run build --filter=highpower", "//build-platforms": "=== 平台构建 ===", "build:mattverse:win": "turbo run build:win --filter=mattverse", "build:mattverse:mac": "turbo run build:mac --filter=mattverse", "build:mattverse:linux": "turbo run build:linux --filter=mattverse", "build:highpower:win": "turbo run build:win --filter=highpower", "build:highpower:mac": "turbo run build:mac --filter=highpower", "build:highpower:linux": "turbo run build:linux --filter=highpower", "build:all-platforms": "node scripts/build/build-all-platforms.js", "//dev": "=== 开发命令 ===", "dev": "turbo run dev", "dev:mattverse": "turbo run dev --filter=mattverse", "dev:highpower": "turbo run dev --filter=highpower", "dev:ui": "turbo run dev --filter=@mattverse/mattverse-ui", "dev:flow": "turbo run dev --filter=@mattverse/mattverse-flow", "dev:manager": "node scripts/dev/dev-manager.js", "//preview": "=== 预览命令 ===", "preview": "turbo run preview", "preview:mattverse": "turbo run preview --filter=mattverse", "preview:highpower": "turbo run preview --filter=highpower", "//quality": "=== 代码质量 ===", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "typecheck": "turbo run typecheck", "//test": "=== 测试命令 ===", "test": "turbo run test", "test:watch": "turbo run test:watch", "//clean": "=== 清理命令 ===", "clean": "turbo run clean", "clean:all": "turbo run clean && rimraf node_modules/.cache .turbo", "clean:deps": "rimraf node_modules pnpm-lock.yaml && pnpm install", "clean:build": "rimraf apps/*/dist apps/*/out packages/*/dist", "//release": "=== 发布管理 ===", "changeset": "changeset", "changeset:version": "changeset version", "changeset:publish": "changeset publish", "version": "changeset version", "release": "changeset publish"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@grpc/grpc-js": "^1.12.0", "@grpc/proto-loader": "^0.7.0", "@unovis/vue": "^1.5.2", "@vue-flow/background": "^1.3.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.45.0", "@vue-flow/minimap": "^1.5.0", "@vueuse/core": "^11.0.0", "animejs": "^4.0.2", "axios": "^1.7.0", "dayjs": "^1.11.0", "dexie": "^4.0.0", "dotenv": "^17.2.1", "echarts": "^5.5.0", "electron-log": "^5.2.0", "gsap": "^3.12.0", "lucide-vue-next": "^0.447.0", "nanoid": "^5.0.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.1.0", "plotly.js": "^2.35.0", "protobufjs": "^7.4.0", "radix-vue": "^1.9.0", "three": "^0.169.0", "vee-validate": "^4.13.0", "vue": "^3.5.0", "vue-i18n": "^10.0.0", "vue-router": "^4.4.0", "vue-sonner": "^1.2.0", "zod": "^3.23.0"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/markdown-it": "^14.1.2", "@types/node": "^22.16.5", "@types/plotly.js": "^3.0.2", "@types/three": "^0.169.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitejs/plugin-vue": "^5.0.0", "autoprefixer": "^10.4.0", "commitizen": "^4.3.1", "concurrently": "^8.2.0", "cz-git": "^1.11.1", "electron": "^33.0.0", "electron-builder": "^25.0.0", "electron-vite": "^2.3.0", "eslint": "^9.0.0", "eslint-plugin-vue": "^9.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8.4.0", "prettier": "^3.2.4", "rimraf": "^6.0.1", "sass": "^1.80.0", "tailwindcss": "^4.1.11", "tsup": "^8.0.0", "turbo": "latest", "typescript": "^5.8.2", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^5.4.0", "vue-tsc": "^2.0.0"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write"], "*.{scss,css}": ["prettier --write"], "*.md": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}, "electron_mirror": "https://npmmirror.com/mirrors/electron/", "electron-builder-binaries_mirror": "https://npmmirror.com/mirrors/electron-builder-binaries/"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}