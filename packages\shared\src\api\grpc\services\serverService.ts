/**
 * 服务器相关的 gRPC 服务
 */
import { BaseService } from './baseService'
import type { GetRequest, DbResponse, GeneralResponse } from '../../../types/grpc'

export type GetServerListRequest = Pick<GetRequest, 'user_id' | 'token'> & {
  id: string
}

export type GetServerListResponse = DbResponse

export type GetVersionRequest = Pick<GetRequest, 'user_id' | 'token'>

export type GetVersionResponse = GeneralResponse & {
  data: {
    version?: string
    api_version?: string
    build_time?: string
    git_commit?: string
  }
}

export class ServerService extends BaseService {
  /**
   * 获取服务器列表
   * @param params 请求参数
   * @returns Promise<GetServerListResponse>
   */
  async getServerList(params: GetServerListRequest): Promise<GetServerListResponse> {
    return this.call<GetServerListResponse>('getServerList', params)
  }

  /**
   * 获取中台版本信息
   * @param params 请求参数
   * @returns Promise<GetVersionResponse>
   */
  async getVersion(params: GetVersionRequest): Promise<GetVersionResponse> {
    return this.call<GetVersionResponse>('getVersion', params)
  }
}

// 导出单例实例
export const serverService = new ServerService()
